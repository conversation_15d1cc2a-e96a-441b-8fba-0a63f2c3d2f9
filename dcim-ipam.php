<?php
/**
 * DCIM IPAM Functions - IP Address Management
 * 
 * @package    DCIM
 * <AUTHOR> Name
 * @copyright  2025
 * @version    1.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use WHMCS\Database\Capsule;

/**
 * ========================================
 * IPAM (IP Address Management) Functions
 * ========================================
 */

/**
 * Convert IP address to long integer for sorting/calculation
 */
function dcim_ip_to_long($ip) {
    return ip2long($ip);
}

/**
 * Convert long integer back to IP address
 */
function dcim_long_to_ip($long) {
    return long2ip($long);
}

/**
 * Calculate network address from IP and prefix length
 */
function dcim_calculate_network($ip, $prefix_length) {
    $ip_long = dcim_ip_to_long($ip);
    $mask = ~((1 << (32 - $prefix_length)) - 1);
    return dcim_long_to_ip($ip_long & $mask);
}

/**
 * Calculate broadcast address from network and prefix length
 */
function dcim_calculate_broadcast($network, $prefix_length) {
    $network_long = dcim_ip_to_long($network);
    $host_bits = 32 - $prefix_length;
    $broadcast_long = $network_long | ((1 << $host_bits) - 1);
    return dcim_long_to_ip($broadcast_long);
}

/**
 * Calculate total IP addresses in a subnet
 */
function dcim_calculate_total_ips($prefix_length) {
    return pow(2, 32 - $prefix_length);
}

/**
 * Calculate usable IP addresses in a subnet (excluding network and broadcast)
 */
function dcim_calculate_usable_ips($prefix_length) {
    $total = dcim_calculate_total_ips($prefix_length);
    return max(0, $total - 2); // Subtract network and broadcast addresses
}

/**
 * Validate IP address format
 */
function dcim_validate_ip($ip) {
    return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) !== false;
}

/**
 * Validate CIDR notation
 */
function dcim_validate_cidr($cidr) {
    if (!preg_match('/^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\/(\d{1,2})$/', $cidr, $matches)) {
        return false;
    }
    
    $ip = $matches[1];
    $prefix = intval($matches[2]);
    
    return dcim_validate_ip($ip) && $prefix >= 0 && $prefix <= 32;
}

/**
 * Check if an IP address is within a subnet
 */
function dcim_ip_in_subnet($ip, $network, $prefix_length) {
    $ip_long = dcim_ip_to_long($ip);
    $network_long = dcim_ip_to_long($network);
    $mask = ~((1 << (32 - $prefix_length)) - 1);
    
    return ($ip_long & $mask) === ($network_long & $mask);
}

/**
 * Create a new subnet
 */
function dcim_create_subnet($name, $network, $prefix_length, $location_id = null, $gateway = null, $dns_primary = null, $dns_secondary = null, $vlan_id = null, $description = null) {
    try {
        // Validate network address
        if (!dcim_validate_ip($network)) {
            return ['success' => false, 'error' => 'Invalid network address'];
        }
        
        // Validate prefix length
        if ($prefix_length < 0 || $prefix_length > 32) {
            return ['success' => false, 'error' => 'Invalid prefix length'];
        }
        
        // Calculate actual network address
        $actual_network = dcim_calculate_network($network, $prefix_length);
        
        // Check if subnet already exists
        $existing = Capsule::table('dcim_subnets')
            ->where('network', $actual_network)
            ->where('prefix_length', $prefix_length)
            ->first();
            
        if ($existing) {
            return ['success' => false, 'error' => 'Subnet already exists'];
        }
        
        // Create subnet
        $subnet_id = Capsule::table('dcim_subnets')->insertGetId([
            'name' => $name,
            'network' => $actual_network,
            'prefix_length' => $prefix_length,
            'location_id' => $location_id,
            'gateway' => $gateway,
            'dns_primary' => $dns_primary,
            'dns_secondary' => $dns_secondary,
            'vlan_id' => $vlan_id,
            'description' => $description,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        // Generate IP addresses for the subnet
        dcim_generate_ip_addresses($subnet_id, $actual_network, $prefix_length);
        
        return ['success' => true, 'subnet_id' => $subnet_id];
        
    } catch (Exception $e) {
        error_log("DCIM: Error creating subnet - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Generate IP addresses for a subnet
 */
function dcim_generate_ip_addresses($subnet_id, $network, $prefix_length) {
    try {
        $network_long = dcim_ip_to_long($network);
        $total_ips = dcim_calculate_total_ips($prefix_length);
        
        $ip_records = [];
        for ($i = 0; $i < $total_ips; $i++) {
            $ip_long = $network_long + $i;
            $ip_address = dcim_long_to_ip($ip_long);
            
            // Determine status based on IP type
            $status = 'available';
            if ($i === 0) {
                $status = 'reserved'; // Network address
            } elseif ($i === $total_ips - 1 && $prefix_length < 31) {
                $status = 'reserved'; // Broadcast address
            }
            
            $ip_records[] = [
                'subnet_id' => $subnet_id,
                'ip_address' => $ip_address,
                'status' => $status,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }
        
        // Insert in batches for better performance
        $chunks = array_chunk($ip_records, 100);
        foreach ($chunks as $chunk) {
            Capsule::table('dcim_ip_addresses')->insert($chunk);
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("DCIM: Error generating IP addresses - " . $e->getMessage());
        return false;
    }
}

/**
 * Divide a subnet into smaller subnets
 */
function dcim_divide_subnet($subnet_id, $new_prefix_length) {
    try {
        // Get parent subnet
        $parent_subnet = Capsule::table('dcim_subnets')->where('id', $subnet_id)->first();
        if (!$parent_subnet) {
            return ['success' => false, 'error' => 'Subnet not found'];
        }
        
        // Validate new prefix length
        if ($new_prefix_length <= $parent_subnet->prefix_length || $new_prefix_length > 30) {
            return ['success' => false, 'error' => 'Invalid new prefix length'];
        }
        
        // Calculate number of new subnets
        $prefix_diff = $new_prefix_length - $parent_subnet->prefix_length;
        $num_subnets = pow(2, $prefix_diff);
        $subnet_size = dcim_calculate_total_ips($new_prefix_length);
        
        $parent_network_long = dcim_ip_to_long($parent_subnet->network);
        $created_subnets = [];
        
        // Create new subnets
        for ($i = 0; $i < $num_subnets; $i++) {
            $subnet_network_long = $parent_network_long + ($i * $subnet_size);
            $subnet_network = dcim_long_to_ip($subnet_network_long);
            
            $new_subnet_id = Capsule::table('dcim_subnets')->insertGetId([
                'name' => $parent_subnet->name . ' - Subnet ' . ($i + 1),
                'network' => $subnet_network,
                'prefix_length' => $new_prefix_length,
                'location_id' => $parent_subnet->location_id,
                'gateway' => null, // Will need to be set manually
                'dns_primary' => $parent_subnet->dns_primary,
                'dns_secondary' => $parent_subnet->dns_secondary,
                'vlan_id' => $parent_subnet->vlan_id,
                'description' => "Created by dividing {$parent_subnet->name}",
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            // Generate IP addresses for new subnet
            dcim_generate_ip_addresses($new_subnet_id, $subnet_network, $new_prefix_length);
            
            $created_subnets[] = [
                'id' => $new_subnet_id,
                'network' => $subnet_network,
                'prefix_length' => $new_prefix_length
            ];
        }
        
        // Mark parent subnet as deprecated
        Capsule::table('dcim_subnets')
            ->where('id', $subnet_id)
            ->update(['status' => 'deprecated', 'updated_at' => date('Y-m-d H:i:s')]);
        
        return ['success' => true, 'created_subnets' => $created_subnets];
        
    } catch (Exception $e) {
        error_log("DCIM: Error dividing subnet - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Get available IP addresses in a subnet
 */
function dcim_get_available_ips($subnet_id, $limit = 50) {
    try {
        return Capsule::table('dcim_ip_addresses')
            ->where('subnet_id', $subnet_id)
            ->where('status', 'available')
            ->limit($limit)
            ->orderByRaw('INET_ATON(ip_address)')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching available IPs - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Assign IP address to a device
 */
function dcim_assign_ip($ip_address_id, $device_type, $device_id, $interface_type = 'primary') {
    try {
        // Check if IP is available
        $ip = Capsule::table('dcim_ip_addresses')->where('id', $ip_address_id)->first();
        if (!$ip || $ip->status !== 'available') {
            return ['success' => false, 'error' => 'IP address not available'];
        }
        
        // Create assignment
        $assignment_id = Capsule::table('dcim_ip_assignments')->insertGetId([
            'ip_address_id' => $ip_address_id,
            'device_type' => $device_type,
            'device_id' => $device_id,
            'interface_type' => $interface_type,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        // Mark IP as assigned
        Capsule::table('dcim_ip_addresses')
            ->where('id', $ip_address_id)
            ->update(['status' => 'assigned', 'updated_at' => date('Y-m-d H:i:s')]);
            
        // Update device IP field based on device type and interface
        dcim_update_device_ip($device_type, $device_id, $interface_type, $ip->ip_address);
        
        return ['success' => true, 'assignment_id' => $assignment_id];
        
    } catch (Exception $e) {
        error_log("DCIM: Error assigning IP - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Update device IP field in the corresponding table
 */
function dcim_update_device_ip($device_type, $device_id, $interface_type, $ip_address) {
    try {
        $table = 'dcim_' . $device_type . 's';
        $field = ($interface_type === 'management') ? 'management_ip' : 'ip_address';
        
        Capsule::table($table)
            ->where('id', $device_id)
            ->update([$field => $ip_address, 'updated_at' => date('Y-m-d H:i:s')]);
            
    } catch (Exception $e) {
        error_log("DCIM: Error updating device IP - " . $e->getMessage());
    }
}

/**
 * Get subnet utilization statistics
 */
function dcim_get_subnet_stats($subnet_id) {
    try {
        $stats = Capsule::table('dcim_ip_addresses')
            ->where('subnet_id', $subnet_id)
            ->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN status = "available" THEN 1 ELSE 0 END) as available,
                SUM(CASE WHEN status = "assigned" THEN 1 ELSE 0 END) as assigned,
                SUM(CASE WHEN status = "reserved" THEN 1 ELSE 0 END) as reserved
            ')
            ->first();
            
        $stats->used_percentage = $stats->total > 0 ? round(($stats->assigned / $stats->total) * 100, 2) : 0;
        
        return $stats;
        
    } catch (Exception $e) {
        error_log("DCIM: Error fetching subnet stats - " . $e->getMessage());
        return null;
    }
}

/**
 * ========================================
 * IPAM Management Interface Functions
 * ========================================
 */

/**
 * Main IPAM management router
 */
function dcim_manage_ipam($modulelink) {
    // Handle AJAX subnet creation
    if ($_POST['action'] == 'add_subnet_ajax') {
        header('Content-Type: application/json');

        try {
            // Parse CIDR notation
            $subnet_cidr = $_POST['subnet_cidr'];
            if (!preg_match('/^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\/(\d{1,2})$/', $subnet_cidr, $matches)) {
                echo json_encode(['success' => false, 'error' => 'Invalid CIDR notation']);
                return;
            }

            $network = $matches[1];
            $prefix_length = intval($matches[2]);

            // Create subnet name from city and network
            $city = $_POST['city'] ?: 'Unknown';
            $subnet_name = $city . ' - ' . $subnet_cidr;

            // Create description from form data
            $description_parts = [];
            if ($_POST['country']) {
                $description_parts[] = 'Country: ' . $_POST['country'];
            }
            if ($_POST['city']) {
                $description_parts[] = 'City: ' . $_POST['city'];
            }
            if ($_POST['public_subnet']) {
                $description_parts[] = 'Public: ' . ($_POST['public_subnet'] == 'yes' ? 'Yes' : 'No');
            }
            if ($_POST['subnet_type']) {
                $description_parts[] = 'Type: ' . ucfirst($_POST['subnet_type']);
            }
            if ($_POST['note']) {
                $description_parts[] = 'Note: ' . $_POST['note'];
            }

            $description = implode(' | ', $description_parts);

            // Create the subnet
            $result = dcim_create_subnet(
                $subnet_name,
                $network,
                $prefix_length,
                null, // location_id - we'll use the description for now
                null, // gateway - can be set later
                null, // dns_primary
                null, // dns_secondary
                null, // vlan_id
                $description
            );

            echo json_encode($result);
            return;

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            return;
        }
    }

    $subaction = $_GET['subaction'] ?? 'dashboard';

    switch ($subaction) {
        case 'subnets':
            dcim_manage_subnets($modulelink);
            break;
        case 'ips':
            dcim_manage_ip_addresses($modulelink);
            break;
        case 'allocation':
            dcim_manage_ip_allocation($modulelink);
            break;
        case 'dashboard':
        default:
            dcim_ipam_dashboard($modulelink);
            break;
    }
}

/**
 * IPAM Dashboard
 */
function dcim_ipam_dashboard($modulelink) {
    // Get IPAM statistics
    try {
        $total_subnets = Capsule::table('dcim_subnets')->where('status', 'active')->count();
        $total_ips = Capsule::table('dcim_ip_addresses')->count();
        $assigned_ips = Capsule::table('dcim_ip_addresses')->where('status', 'assigned')->count();
        $available_ips = Capsule::table('dcim_ip_addresses')->where('status', 'available')->count();
        $reserved_ips = Capsule::table('dcim_ip_addresses')->where('status', 'reserved')->count();
        
        // Get recent subnet activity
        $recent_subnets = Capsule::table('dcim_subnets')
            ->leftJoin('dcim_locations', 'dcim_subnets.location_id', '=', 'dcim_locations.id')
            ->select('dcim_subnets.*', 'dcim_locations.name as location_name')
            ->orderBy('dcim_subnets.created_at', 'desc')
            ->limit(5)
            ->get();
            
        // Get subnet utilization data
        $subnet_utilization = Capsule::table('dcim_subnets')
            ->leftJoin('dcim_ip_addresses', 'dcim_subnets.id', '=', 'dcim_ip_addresses.subnet_id')
            ->selectRaw('
                dcim_subnets.id,
                dcim_subnets.name,
                dcim_subnets.network,
                dcim_subnets.prefix_length,
                COUNT(dcim_ip_addresses.id) as total_ips,
                SUM(CASE WHEN dcim_ip_addresses.status = "assigned" THEN 1 ELSE 0 END) as assigned_ips
            ')
            ->where('dcim_subnets.status', 'active')
            ->groupBy('dcim_subnets.id', 'dcim_subnets.name', 'dcim_subnets.network', 'dcim_subnets.prefix_length')
            ->get();
            
    } catch (Exception $e) {
        error_log("DCIM: Error fetching IPAM stats - " . $e->getMessage());
        $total_subnets = $total_ips = $assigned_ips = $available_ips = $reserved_ips = 0;
        $recent_subnets = collect([]);
        $subnet_utilization = collect([]);
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">';
    echo '<i class="fas fa-network-wired" style="margin-right: 12px; color: #059669;"></i>';
    echo 'IPAM Dashboard';
    echo '</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button class="add-location-btn" onclick="alert(\'Button clicked!\'); showAddSubnetModal();">';
    echo '<i class="fas fa-plus"></i> Add Subnet';
    echo '</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Statistics Overview
    echo '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 24px; margin-bottom: 32px;">';
    
    echo '<div class="stats-card">';
    echo '<div class="stats-icon" style="background: #e0f2fe; color: #0277bd;"><i class="fas fa-sitemap"></i></div>';
    echo '<div class="stats-content">';
    echo '<div class="stats-number">' . $total_subnets . '</div>';
    echo '<div class="stats-label">Active Subnets</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="stats-card">';
    echo '<div class="stats-icon" style="background: #f3e5f5; color: #7b1fa2;"><i class="fas fa-list-ol"></i></div>';
    echo '<div class="stats-content">';
    echo '<div class="stats-number">' . number_format($total_ips) . '</div>';
    echo '<div class="stats-label">Total IP Addresses</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="stats-card">';
    echo '<div class="stats-icon" style="background: #e8f5e8; color: #2e7d32;"><i class="fas fa-check-circle"></i></div>';
    echo '<div class="stats-content">';
    echo '<div class="stats-number">' . number_format($assigned_ips) . '</div>';
    echo '<div class="stats-label">Assigned IPs</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="stats-card">';
    echo '<div class="stats-icon" style="background: #fff3e0; color: #ef6c00;"><i class="fas fa-circle"></i></div>';
    echo '<div class="stats-content">';
    echo '<div class="stats-number">' . number_format($available_ips) . '</div>';
    echo '<div class="stats-label">Available IPs</div>';
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    
    // Subnet Utilization Chart
    if (count($subnet_utilization) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; padding: 24px; margin-bottom: 24px;">';
        echo '<h3 style="margin: 0 0 20px 0; color: #111827; font-weight: 600;">Subnet Utilization</h3>';
        echo '<div class="subnet-utilization-grid">';
        
        foreach ($subnet_utilization as $subnet) {
            $utilization = $subnet->total_ips > 0 ? round(($subnet->assigned_ips / $subnet->total_ips) * 100, 1) : 0;
            $color = $utilization > 80 ? '#ef4444' : ($utilization > 60 ? '#f59e0b' : '#10b981');
            
            echo '<div class="subnet-util-item">';
            echo '<div class="subnet-name">' . htmlspecialchars($subnet->name) . '</div>';
            echo '<div class="subnet-network">' . $subnet->network . '/' . $subnet->prefix_length . '</div>';
            echo '<div class="utilization-bar">';
            echo '<div class="utilization-fill" style="width: ' . $utilization . '%; background: ' . $color . ';"></div>';
            echo '</div>';
            echo '<div class="utilization-text" style="color: ' . $color . ';">' . $utilization . '% (' . $subnet->assigned_ips . '/' . $subnet->total_ips . ')</div>';
            echo '</div>';
        }
        
        echo '</div>';
        echo '</div>';
    }
    
    // Recent Subnets
    if (count($recent_subnets) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden;">';
        echo '<div style="padding: 24px; border-bottom: 1px solid #e5e7eb; background: #f8fafc;">';
        echo '<h3 style="margin: 0; color: #111827; font-weight: 600;">Recent Subnets</h3>';
        echo '</div>';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Name</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Network</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Location</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Gateway</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Status</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($recent_subnets as $subnet) {
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px; font-weight: 600; color: #111827;">' . htmlspecialchars($subnet->name) . '</td>';
            echo '<td style="padding: 16px; color: #6b7280; font-family: monospace;">' . $subnet->network . '/' . $subnet->prefix_length . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($subnet->location_name ? htmlspecialchars($subnet->location_name) : 'Global') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280; font-family: monospace;">' . ($subnet->gateway ?: '-') . '</td>';
            echo '<td style="padding: 16px;">';
            $status_colors = ['active' => '#10b981', 'reserved' => '#f59e0b', 'deprecated' => '#6b7280'];
            $color = $status_colors[$subnet->status] ?? '#6b7280';
            echo '<span style="background: ' . $color . '; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">' . ucfirst($subnet->status) . '</span>';
            echo '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="viewSubnet(' . $subnet->id . ')" style="background: #4f46e5; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-eye"></i> View';
            echo '</button>';
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';

    // Add Subnet Modal
    dcim_render_add_subnet_modal($modulelink);

    // Add CSS for IPAM Dashboard
    echo '<style>
    .stats-card {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 24px;
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.2s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        flex-shrink: 0;
    }
    
    .stats-content {
        flex: 1;
    }
    
    .stats-number {
        font-size: 32px;
        font-weight: 700;
        color: #111827;
        line-height: 1;
        margin-bottom: 4px;
    }
    
    .stats-label {
        font-size: 14px;
        color: #6b7280;
        font-weight: 500;
    }
    
    .subnet-utilization-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }
    
    .subnet-util-item {
        padding: 16px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        background: #f9fafb;
    }
    
    .subnet-name {
        font-weight: 600;
        color: #111827;
        margin-bottom: 4px;
    }
    
    .subnet-network {
        font-family: monospace;
        color: #6b7280;
        font-size: 14px;
        margin-bottom: 12px;
    }
    
    .utilization-bar {
        height: 8px;
        background: #e5e7eb;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 8px;
    }
    
    .utilization-fill {
        height: 100%;
        transition: width 0.3s ease;
    }
    
    .utilization-text {
        font-size: 12px;
        font-weight: 500;
    }

    /* Modal specific styles */
    .dcim-modal-overlay {
        animation: modalFadeIn 0.3s ease-out;
    }

    .dcim-modal-content {
        animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalFadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes modalSlideIn {
        from {
            transform: scale(0.95);
            opacity: 0;
        }
        to {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* Radio button styling */
    input[type="radio"] {
        accent-color: #4f46e5;
    }

    /* Button hover effects */
    button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    button:active {
        transform: translateY(0);
    }

    /* Close button hover effect */
    button[onclick="closeSubnetModal()"]:hover {
        background: #f3f4f6;
        color: #111827;
    }
    </style>';
    
    // Add JavaScript
    echo '<script>
    // Debug: Check if script is loading
    console.log("IPAM JavaScript loaded");

    window.addEventListener("DOMContentLoaded", function() {
        console.log("DOM loaded, checking for modal...");
        const modal = document.getElementById("addSubnetModal");
        console.log("Modal found on load:", modal);

        // Test: Try to show modal for 2 seconds to see if it's visible
        if (modal) {
            console.log("Testing modal visibility...");
            modal.style.display = "flex";
            setTimeout(function() {
                modal.style.display = "none";
                console.log("Modal test completed");
            }, 2000);
        }
    });

    function viewSubnet(subnetId) {
        window.location.href = "' . $modulelink . '&action=ipam&subaction=subnets&view=" + subnetId;
    }

    function showAddSubnetModal() {
        console.log("showAddSubnetModal called");
        const modal = document.getElementById("addSubnetModal");
        console.log("Modal element:", modal);
        console.log("Modal current display style:", modal ? modal.style.display : "modal not found");

        if (modal) {
            console.log("Setting modal display to flex...");
            modal.style.display = "flex";
            console.log("Modal display after setting:", modal.style.display);
            console.log("Modal computed style:", window.getComputedStyle(modal).display);

            // Reset form
            const form = modal.querySelector("form");
            if (form) {
                form.reset();
                console.log("Form reset");
            } else {
                console.log("Form not found in modal");
            }
        } else {
            console.error("Modal not found!");
        }
    }

    function closeSubnetModal() {
        const modal = document.getElementById("addSubnetModal");
        if (modal) {
            modal.style.display = "none";
        }
    }

    function submitSubnetForm(event) {
        event.preventDefault();
        const form = event.target;
        const formData = new FormData(form);
        formData.append("action", "add_subnet_ajax");

        // Show loading state
        const submitBtn = form.querySelector("button[type=submit]");
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> Creating...";
        submitBtn.disabled = true;

        fetch("' . $modulelink . '&action=ipam", {
            method: "POST",
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                closeSubnetModal();
                // Show success message and reload page
                alert("Subnet created successfully!");
                window.location.reload();
            } else {
                alert("Error creating subnet: " + data.error);
            }
        })
        .catch(error => {
            console.error("Error:", error);
            alert("An error occurred while creating the subnet.");
        })
        .finally(() => {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    }
    </script>';
    
    // Use shared sidebar JavaScript
    dcim_generate_sidebar_javascript($modulelink);
}

/**
 * Manage Subnets
 */
function dcim_manage_subnets($modulelink) {
    // Handle form submissions
    if ($_POST['action'] == 'add_subnet') {
        $result = dcim_create_subnet(
            $_POST['name'],
            $_POST['network'],
            $_POST['prefix_length'],
            $_POST['location_id'] ?: null,
            $_POST['gateway'] ?: null,
            $_POST['dns_primary'] ?: null,
            $_POST['dns_secondary'] ?: null,
            $_POST['vlan_id'] ?: null,
            $_POST['description'] ?: null
        );
        
        if ($result['success']) {
            echo '<div class="alert alert-success">Subnet created successfully!</div>';
        } else {
            echo '<div class="alert alert-danger">Error creating subnet: ' . $result['error'] . '</div>';
        }
    }
    
    if ($_POST['action'] == 'divide_subnet') {
        $result = dcim_divide_subnet($_POST['subnet_id'], $_POST['new_prefix_length']);
        
        if ($result['success']) {
            echo '<div class="alert alert-success">Subnet divided successfully! Created ' . count($result['created_subnets']) . ' new subnets.</div>';
        } else {
            echo '<div class="alert alert-danger">Error dividing subnet: ' . $result['error'] . '</div>';
        }
    }
    
    if ($_GET['delete']) {
        try {
            Capsule::table('dcim_subnets')->where('id', $_GET['delete'])->delete();
            echo '<div class="alert alert-success">Subnet deleted successfully!</div>';
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Error deleting subnet: ' . $e->getMessage() . '</div>';
        }
    }
    
    // Get data
    try {
        $locations = Capsule::table('dcim_locations')->get();
        $subnets = Capsule::table('dcim_subnets')
            ->leftJoin('dcim_locations', 'dcim_subnets.location_id', '=', 'dcim_locations.id')
            ->select('dcim_subnets.*', 'dcim_locations.name as location_name')
            ->orderBy('dcim_subnets.created_at', 'desc')
            ->get();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching data: ' . $e->getMessage() . '</div>';
        $locations = collect([]);
        $subnets = collect([]);
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<button class="back-btn" onclick="goBack()">';
    echo '<i class="fas fa-arrow-left"></i>';
    echo '</button>';
    echo '<div class="main-title">Subnet Management</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Add subnet form
    echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; padding: 32px; margin-bottom: 24px;">';
    echo '<h3 style="margin: 0 0 24px 0; color: #111827; font-weight: 600;">Add New Subnet</h3>';
    echo '<form method="post">';
    echo '<input type="hidden" name="action" value="add_subnet">';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Subnet Name *</label>';
    echo '<input type="text" name="name" required placeholder="e.g., Production Network" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Network Address *</label>';
    echo '<input type="text" name="network" required placeholder="e.g., ***********" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Prefix Length *</label>';
    echo '<select name="prefix_length" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    for ($i = 8; $i <= 30; $i++) {
        $hosts = pow(2, 32 - $i);
        echo '<option value="' . $i . '">/' . $i . ' (' . number_format($hosts) . ' addresses)</option>';
    }
    echo '</select>';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Location</label>';
    echo '<select name="location_id" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '<option value="">Global (No specific location)</option>';
    foreach ($locations as $location) {
        echo '<option value="' . $location->id . '">' . htmlspecialchars($location->name) . '</option>';
    }
    echo '</select>';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Gateway</label>';
    echo '<input type="text" name="gateway" placeholder="e.g., ***********" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Primary DNS</label>';
    echo '<input type="text" name="dns_primary" placeholder="e.g., 8.8.8.8" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Secondary DNS</label>';
    echo '<input type="text" name="dns_secondary" placeholder="e.g., 8.8.4.4" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">VLAN ID</label>';
    echo '<input type="text" name="vlan_id" placeholder="e.g., 100" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '</div>';
    
    echo '<div style="margin-bottom: 24px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Description</label>';
    echo '<textarea name="description" rows="3" placeholder="Optional description..." style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; resize: vertical;"></textarea>';
    echo '</div>';
    
    echo '<button type="submit" class="add-location-btn" style="padding: 12px 24px;">';
    echo '<i class="fas fa-plus"></i> Create Subnet';
    echo '</button>';
    echo '</form>';
    echo '</div>';
    
    // Existing subnets
    if (count($subnets) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden;">';
        echo '<div style="padding: 24px; border-bottom: 1px solid #e5e7eb; background: #f8fafc;">';
        echo '<h3 style="margin: 0; color: #111827; font-weight: 600;">Existing Subnets</h3>';
        echo '</div>';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Name</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Network</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Location</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Gateway</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Status</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($subnets as $subnet) {
            // Get utilization stats
            $stats = dcim_get_subnet_stats($subnet->id);
            
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-weight: 600; color: #111827;">' . htmlspecialchars($subnet->name) . '</div>';
            if ($subnet->description) {
                echo '<div style="font-size: 12px; color: #6b7280; margin-top: 4px;">' . htmlspecialchars($subnet->description) . '</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-family: monospace; color: #111827;">' . $subnet->network . '/' . $subnet->prefix_length . '</div>';
            if ($stats) {
                echo '<div style="font-size: 12px; color: #6b7280; margin-top: 4px;">' . number_format($stats->total) . ' total IPs</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($subnet->location_name ? htmlspecialchars($subnet->location_name) : 'Global') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280; font-family: monospace;">' . ($subnet->gateway ?: '-') . '</td>';
            echo '<td style="padding: 16px;">';
            $status_colors = ['active' => '#10b981', 'reserved' => '#f59e0b', 'deprecated' => '#6b7280'];
            $color = $status_colors[$subnet->status] ?? '#6b7280';
            echo '<span style="background: ' . $color . '; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">' . ucfirst($subnet->status) . '</span>';
            if ($stats && $stats->total > 0) {
                echo '<div style="font-size: 12px; color: #6b7280; margin-top: 4px;">' . $stats->used_percentage . '% used</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="viewSubnetIPs(' . $subnet->id . ')" style="background: #4f46e5; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-list"></i> IPs';
            echo '</button>';
            echo '<button onclick="divideSubnet(' . $subnet->id . ')" style="background: #059669; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-cut"></i> Divide';
            echo '</button>';
            echo '<button onclick="deleteSubnet(' . $subnet->id . ')" style="background: #ef4444; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-trash"></i>';
            echo '</button>';
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Use shared sidebar JavaScript and add page-specific functions
    dcim_generate_sidebar_javascript($modulelink);
    echo '<script>
    function goBack() {
        window.location.href = "' . $modulelink . '&action=ipam";
    }
    
    function viewSubnetIPs(subnetId) {
        window.location.href = "' . $modulelink . '&action=ipam&subaction=ips&subnet_id=" + subnetId;
    }
    
    function divideSubnet(subnetId) {
        const newPrefix = prompt("Enter new prefix length (must be larger than current):");
        if (newPrefix && !isNaN(newPrefix)) {
            const form = document.createElement("form");
            form.method = "post";
            form.innerHTML = `
                <input type="hidden" name="action" value="divide_subnet">
                <input type="hidden" name="subnet_id" value="${subnetId}">
                <input type="hidden" name="new_prefix_length" value="${newPrefix}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    function deleteSubnet(subnetId) {
        if (confirm("Are you sure you want to delete this subnet and all its IP addresses?")) {
            window.location.href = "' . $modulelink . '&action=ipam&subaction=subnets&delete=" + subnetId;
        }
    }
    </script>';
}

/**
 * Placeholder functions for other IPAM interfaces
 */
function dcim_manage_ip_addresses($modulelink) {
    echo '<div class="dcim-container">Coming soon: IP Address Management</div>';
}

function dcim_manage_ip_allocation($modulelink) {
    echo '<div class="dcim-container">Coming soon: IP Allocation Interface</div>';
}

/**
 * Render Add Subnet Modal
 */
function dcim_render_add_subnet_modal($modulelink) {
    // Get locations and countries for dropdowns
    try {
        $locations = Capsule::table('dcim_locations')->orderBy('name')->get();

        // Get unique countries from locations
        $countries = Capsule::table('dcim_locations')
            ->whereNotNull('country')
            ->where('country', '!=', '')
            ->distinct()
            ->pluck('country')
            ->sort()
            ->values();

        // Get unique cities from locations
        $cities = Capsule::table('dcim_locations')
            ->whereNotNull('city')
            ->where('city', '!=', '')
            ->distinct()
            ->pluck('city')
            ->sort()
            ->values();
    } catch (Exception $e) {
        $locations = collect([]);
        $countries = collect([]);
        $cities = collect([]);
    }

    // Add common countries if none exist in database
    if ($countries->isEmpty()) {
        $countries = collect([
            'United States', 'Germany', 'United Kingdom', 'France', 'Canada',
            'Australia', 'Japan', 'Netherlands', 'Sweden', 'Norway', 'Denmark',
            'Finland', 'Switzerland', 'Austria', 'Belgium', 'Ireland', 'Italy',
            'Spain', 'Portugal', 'Poland', 'Czech Republic', 'Hungary', 'Greece'
        ]);
    }

    // Add common cities if none exist in database
    if ($cities->isEmpty()) {
        $cities = collect([
            'New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia',
            'San Antonio', 'San Diego', 'Dallas', 'San Jose', 'Austin', 'Jacksonville',
            'London', 'Manchester', 'Birmingham', 'Leeds', 'Glasgow', 'Sheffield',
            'Berlin', 'Hamburg', 'Munich', 'Cologne', 'Frankfurt', 'Stuttgart',
            'Paris', 'Marseille', 'Lyon', 'Toulouse', 'Nice', 'Nantes',
            'Toronto', 'Montreal', 'Vancouver', 'Calgary', 'Edmonton', 'Ottawa',
            'Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide', 'Gold Coast',
            'Tokyo', 'Yokohama', 'Osaka', 'Nagoya', 'Sapporo', 'Kobe',
            'Amsterdam', 'Rotterdam', 'The Hague', 'Utrecht', 'Eindhoven', 'Tilburg'
        ]);
    }

    echo '<!-- Modal Debug: Rendering addSubnetModal -->';
    echo '<div id="addSubnetModal" class="dcim-modal-overlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.6); z-index: 1100; align-items: center; justify-content: center; padding: 20px;">';
    echo '<div class="dcim-modal-content" style="background: white; border-radius: 12px; box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3); width: 100%; max-width: 600px; max-height: 90vh; overflow-y: auto;">';

    // Modal Header
    echo '<div style="padding: 24px 32px; border-bottom: 1px solid #e5e7eb; display: flex; align-items: center; justify-content: space-between; background: #f8fafc; border-radius: 12px 12px 0 0;">';
    echo '<h3 style="margin: 0; font-size: 20px; font-weight: 600; color: #111827;">Add New Subnet</h3>';
    echo '<button type="button" onclick="closeSubnetModal()" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer; padding: 0; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 6px; transition: all 0.2s;">';
    echo '<i class="fas fa-times"></i>';
    echo '</button>';
    echo '</div>';

    // Modal Body
    echo '<div style="padding: 32px;">';
    echo '<form onsubmit="submitSubnetForm(event)" method="post">';

    // IP Version and Subnet fields
    echo '<div style="display: grid; grid-template-columns: 1fr 2fr; gap: 20px; margin-bottom: 24px;">';

    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">IP Version <span style="color: #ef4444;">*</span></label>';
    echo '<select name="ip_version" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; background: white;">';
    echo '<option value="IPv4" selected>IPv4</option>';
    echo '<option value="IPv6">IPv6</option>';
    echo '</select>';
    echo '</div>';

    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Subnet (CIDR notation) <span style="color: #ef4444;">*</span></label>';
    echo '<input type="text" name="subnet_cidr" required placeholder="e.g., ***********/24" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';

    echo '</div>';

    // Country and City fields
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 24px;">';

    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Country</label>';
    echo '<select name="country" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; background: white;">';
    echo '<option value="">-- Select Country --</option>';
    foreach ($countries as $country) {
        echo '<option value="' . htmlspecialchars($country) . '">' . htmlspecialchars($country) . '</option>';
    }
    echo '</select>';
    echo '</div>';

    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">City <span style="color: #ef4444;">*</span></label>';
    echo '<select name="city" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; background: white;">';
    echo '<option value="">-- Select City --</option>';
    foreach ($cities as $city) {
        echo '<option value="' . htmlspecialchars($city) . '">' . htmlspecialchars($city) . '</option>';
    }
    echo '</select>';
    echo '</div>';

    echo '</div>';

    // Public Subnet radio buttons
    echo '<div style="margin-bottom: 24px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 12px;">Public Subnet</label>';
    echo '<div style="display: flex; gap: 24px;">';
    echo '<label style="display: flex; align-items: center; cursor: pointer;">';
    echo '<input type="radio" name="public_subnet" value="yes" style="margin-right: 8px; width: 16px; height: 16px;">';
    echo '<span style="color: #374151;">Yes</span>';
    echo '</label>';
    echo '<label style="display: flex; align-items: center; cursor: pointer;">';
    echo '<input type="radio" name="public_subnet" value="no" checked style="margin-right: 8px; width: 16px; height: 16px;">';
    echo '<span style="color: #374151;">No</span>';
    echo '</label>';
    echo '</div>';
    echo '</div>';

    // Subnet Type radio buttons
    echo '<div style="margin-bottom: 24px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 12px;">Subnet Type</label>';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">';

    echo '<label style="display: flex; align-items: center; cursor: pointer;">';
    echo '<input type="radio" name="subnet_type" value="root" checked style="margin-right: 8px; width: 16px; height: 16px;">';
    echo '<span style="color: #374151;">Root</span>';
    echo '</label>';

    echo '<label style="display: flex; align-items: center; cursor: pointer;">';
    echo '<input type="radio" name="subnet_type" value="customer" style="margin-right: 8px; width: 16px; height: 16px;">';
    echo '<span style="color: #374151;">Customer</span>';
    echo '</label>';

    echo '<label style="display: flex; align-items: center; cursor: pointer;">';
    echo '<input type="radio" name="subnet_type" value="management" style="margin-right: 8px; width: 16px; height: 16px;">';
    echo '<span style="color: #374151;">Management</span>';
    echo '</label>';

    echo '<label style="display: flex; align-items: center; cursor: pointer;">';
    echo '<input type="radio" name="subnet_type" value="transit" style="margin-right: 8px; width: 16px; height: 16px;">';
    echo '<span style="color: #374151;">Transit</span>';
    echo '</label>';

    echo '</div>';
    echo '</div>';

    // Note field
    echo '<div style="margin-bottom: 32px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Note</label>';
    echo '<textarea name="note" rows="4" placeholder="Optional note for this subnet..." style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; resize: vertical; font-family: inherit;"></textarea>';
    echo '</div>';

    // Modal Footer with buttons
    echo '<div style="display: flex; justify-content: flex-end; gap: 12px; padding-top: 24px; border-top: 1px solid #e5e7eb;">';
    echo '<button type="button" onclick="closeSubnetModal()" style="background: #f3f4f6; color: #374151; border: 1px solid #d1d5db; padding: 12px 24px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s;">';
    echo 'Cancel';
    echo '</button>';
    echo '<button type="submit" style="background: #4f46e5; color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus"></i> Add Subnet';
    echo '</button>';
    echo '</div>';

    echo '</form>';
    echo '</div>';

    echo '</div>';
    echo '</div>';
}

?>